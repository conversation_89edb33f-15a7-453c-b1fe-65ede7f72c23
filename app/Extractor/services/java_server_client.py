"""
Java Server Client Service

Service for sending PATCH requests to Java server with extracted entity data
"""

import httpx
import json
import os
from typing import Dict, Any, Union, Optional
from datetime import datetime

from app.Extractor.utils.logger import EntityExtractorLogger


class JavaServerClient:
    """
    Client for sending PATCH requests to Java server with entity extraction results
    """
    
    def __init__(self, logger: Optional[EntityExtractorLogger] = None):
        self.logger = logger or EntityExtractorLogger(
            analysis_id="java_server_client",
            scrape_request_id="client"
        )
        
        # Get Java server configuration from environment variables
        self.java_server_base_url = os.getenv("JAVA_SERVER_BASE_URL", "http://localhost:8002")
        self.java_server_timeout = int(os.getenv("JAVA_SERVER_TIMEOUT", "30"))
        self.java_server_api_key = os.getenv("JAVA_SERVER_API_KEY")
        
        self.logger.info(f"Java server client initialized with base URL: {self.java_server_base_url}")
    
    async def send_extraction_results(
        self,
        scrape_request_ref_id: str,
        website_url: str,
        org_id: Union[str, int],
        extraction_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Send entity extraction results to Java server via PATCH request
        
        Args:
            scrape_request_ref_id: Reference ID for the scrape request
            website_url: Website URL that was processed
            org_id: Organization ID (preserving original type - int or string)
            extraction_results: Complete extraction results from entity extraction
            
        Returns:
            Response from Java server
        """
        try:
            self.logger.info(f"Sending extraction results to Java server for {website_url}")
            
            # Prepare payload with org_id preserving its original type
            payload = {
                "scrape_request_ref_id": scrape_request_ref_id,
                "website_url": website_url,
                "org_id": org_id,  # Preserve original type (int or string)
                "extraction_results": extraction_results,
                "timestamp": datetime.now().isoformat(),
                "processing_status": "COMPLETED"
            }
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            # Add API key if configured
            if self.java_server_api_key:
                headers["Authorization"] = f"Bearer {self.java_server_api_key}"
            
            # Construct endpoint URL
            endpoint_url = f"{self.java_server_base_url}/api/entity-extraction/results"
            
            self.logger.info(f"Sending PATCH request to: {endpoint_url}")
            self.logger.debug(f"Payload org_id type: {type(org_id)}, value: {org_id}")
            
            # Send PATCH request
            async with httpx.AsyncClient(timeout=self.java_server_timeout) as client:
                response = await client.patch(
                    endpoint_url,
                    json=payload,
                    headers=headers
                )
                
                # Log response
                self.logger.info(f"Java server response status: {response.status_code}")
                
                if response.status_code in [200, 201, 204]:
                    self.logger.info("Successfully sent extraction results to Java server")
                    return {
                        "success": True,
                        "status_code": response.status_code,
                        "response": response.json() if response.content else {},
                        "message": "Results sent successfully"
                    }
                else:
                    self.logger.error(f"Java server returned error: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "status_code": response.status_code,
                        "error": response.text,
                        "message": "Failed to send results to Java server"
                    }
                    
        except httpx.TimeoutException:
            error_msg = f"Timeout sending results to Java server after {self.java_server_timeout}s"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": "timeout",
                "message": error_msg
            }
            
        except httpx.RequestError as e:
            error_msg = f"Request error sending results to Java server: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": "request_error",
                "message": error_msg
            }
            
        except Exception as e:
            error_msg = f"Unexpected error sending results to Java server: {str(e)}"
            self.logger.error(error_msg, error=e)
            return {
                "success": False,
                "error": "unexpected_error",
                "message": error_msg
            }
    
    def send_extraction_results_sync(
        self,
        scrape_request_ref_id: str,
        website_url: str,
        org_id: Union[str, int],
        extraction_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Synchronous version of send_extraction_results for use in background tasks
        
        Args:
            scrape_request_ref_id: Reference ID for the scrape request
            website_url: Website URL that was processed
            org_id: Organization ID (preserving original type - int or string)
            extraction_results: Complete extraction results from entity extraction
            
        Returns:
            Response from Java server
        """
        try:
            self.logger.info(f"Sending extraction results to Java server (sync) for {website_url}")
            
            # Prepare payload with org_id preserving its original type
            payload = {
                "scrape_request_ref_id": scrape_request_ref_id,
                "website_url": website_url,
                "org_id": org_id,  # Preserve original type (int or string)
                "extraction_results": extraction_results,
                "timestamp": datetime.now().isoformat(),
                "processing_status": "COMPLETED"
            }
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            # Add API key if configured
            if self.java_server_api_key:
                headers["Authorization"] = f"Bearer {self.java_server_api_key}"
            
            # Construct endpoint URL
            endpoint_url = f"{self.java_server_base_url}/api/entity-extraction/results"
            
            self.logger.info(f"Sending PATCH request to: {endpoint_url}")
            self.logger.debug(f"Payload org_id type: {type(org_id)}, value: {org_id}")
            
            # Send PATCH request using httpx sync client
            with httpx.Client(timeout=self.java_server_timeout) as client:
                response = client.patch(
                    endpoint_url,
                    json=payload,
                    headers=headers
                )
                
                # Log response
                self.logger.info(f"Java server response status: {response.status_code}")
                
                if response.status_code in [200, 201, 204]:
                    self.logger.info("Successfully sent extraction results to Java server")
                    return {
                        "success": True,
                        "status_code": response.status_code,
                        "response": response.json() if response.content else {},
                        "message": "Results sent successfully"
                    }
                else:
                    self.logger.error(f"Java server returned error: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "status_code": response.status_code,
                        "error": response.text,
                        "message": "Failed to send results to Java server"
                    }
                    
        except httpx.TimeoutException:
            error_msg = f"Timeout sending results to Java server after {self.java_server_timeout}s"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": "timeout",
                "message": error_msg
            }
            
        except httpx.RequestError as e:
            error_msg = f"Request error sending results to Java server: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": "request_error",
                "message": error_msg
            }
            
        except Exception as e:
            error_msg = f"Unexpected error sending results to Java server: {str(e)}"
            self.logger.error(error_msg, error=e)
            return {
                "success": False,
                "error": "unexpected_error",
                "message": error_msg
            }
