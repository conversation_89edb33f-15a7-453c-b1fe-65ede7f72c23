class GeminiEntityPrompts:
    """
    Prompts specifically designed for Gemini AI model
    """
    
    @staticmethod
    def get_entity_extraction_prompt(urls_dict: dict) -> str:
        """
        Generate prompt for extracting business entities from URLs (optimized for Gemini)
        
        Args:
            urls_dict: Dictionary with URL types as keys and URLs as values
                      e.g., {"home": "url1", "terms": "url2"}
        """
        urls_list = []
        for url_type, url in urls_dict.items():
            urls_list.append(f"- {url_type.title()} Page: {url}")

        urls_text = "\n".join(urls_list)
        url_count = len(urls_dict)

        # Adjust instructions based on number of URLs
        if url_count == 1:
            instruction_text = "Visit this URL and carefully examine the content"
        else:
            instruction_text = "Visit these URLs and carefully examine the content"

        return f"""
You are an expert business analyst who has a lot of experience in dealing with business's official website and the detailed information present in websites URLs. 
You will be provided with {url_count} URL{'s' if url_count > 1 else ''} from a website, and you need to extract specific business information from the content present in the URLs.


* Input Starts *
URL{'s' if url_count > 1 else ''} to analyze:
{urls_text}
* Input Ends *


* Tasks Starts *
You will be given company's/business's policy related URLs, they can contain information regarding Terms and conditions, privacy policy, shipping cancellation and return policy, company contact information, home page etc.
Your main task is to extract the following information from these URLs.

1. "legal_name" ---> Legal Name of the Company: a string, The official registered business name (single value only)
2. "business_email" ---> Business Official Email Address: a list [], Primary business contact email/emails as array (contact@, info@, etc.)
3. "support_email" ---> Support Emails: a list [], Customer support email/emails addresses as array (support@, help@, customerservice@, etc.)
4. "contact_form" ---> a string, yes/no, you have to check if there is a dedicated contact us form present on the site, assign yes if its present.
5. "business_contact_numbers" ---> Business Official Contact Numbers: a list[],  Official phone number/numbers for business inquiries as array, extract as it is
6. "business_location" ---> Business Location: a list [], Complete business addresses as array (include all locations if multiple official address is present)
7. "phone_email_address_missing" ---> a list [] containing entries strictly amongst values "email","phone", "address" if they are misisng.
8. "accepts_international_orders" ---> International Shipping Offered : a string, yes or no or not_mentioned, Does the company accept international orders/shipping? (yes/no/not_mentioned). It is assumed that website offered services to Indin. Any country outside India is assumed be international.
9. "international_orders_evidence" --->  a string, some products details, with pricing in NON-INR value. max 3 products and maximum 20 words.  
10. "shipping_countries" ---> Shipping Countries : a list [], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies
11. "shipping_policy_details" ---> Shipping Policy Details: a string, Summary of phrases or snippets which were used to conclude the answer on international shipping details, (not more than 10-15 words)
12. "has_jurisdiction_law" ---> Jurisdiction/Governing Law: a string, yes/no, Does the website specify governing law or jurisdiction in case of any dispute (yes/no)
13. "jurisdiction_place" ---> Jurisdiction/Governing Law location : a list [], this should have the places of which the governing law applies to any disputes
14. "jurisdiction_details" ---> Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/lar related query above, in not more than 10-15 words.

**Instructions:**
- {instruction_text}
- Visit all the URLs and only derive mentioned in the websites, for e.g. do not assume anything, do not read reviews mentioned, or comments in the websites mentioned to extract any official information. 
- Extract only verified information that appears on the website from valid URLs.
- Support email and business official email can be 2 different ids. 
- Business official address is the where office is physically located.
- If information appears on multiple pages, use the most authoritative source or URLs. 
- If a piece of information is not found, return null for that field
- Be precise and avoid making assumptions
- Home page can also have many information. If there is no dedicated URLs or if infomration is not found on the URLs, it can be present in home page as well.

**Output Format:**
Return your response strictly in JSON format with below 14 keys:

Look at the context in and around the entity to extract any required information.

```json
{{
    "legal_name": "Single company legal name or null", a string, provide "" empty string in case not identified.
    "business_email": a list, ["<EMAIL>", "<EMAIL>"], provide empty [] list in case not identified,
    "support_email": a list, ["<EMAIL>", "<EMAIL>"], provide empty [] list in case not identified,
    "contact_form": a string, yes/no, yes if there is a contact form prsent on the site.
    "business_contact_numbers": a list, ["+1234567890", "+0987654321"], provide empty [] list in case not identified,
    "business_location": a list, ["Address 1", "Address 2"], provide empty [] list in case not identified,
    "phone_email_address_missing": a list of entities missing amongst support/company email, business contact information and business contact number.
    "accepts_international_orders": "yes/no", a string
    "international_orders_evidence": a string, some products/services details, with pricing in NON-INR value. max 3 products and maximum 20 words.  
    "shipping_countries":["India","Australia"....], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies, provide empty [] list in case not identified,
    "shipping_policy_details": "Summary of shipping and delivery policies or null", a string.
    "has_jurisdiction_law": "yes/no", Only yes and no, a string.
    "jurisdiction_place": a list [], this should have the places of which the governing law applies to any disputes, provide empty [] list in case not identified,
    "jurisdiction_details" : Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/lar related query above, in not more than 10-15 words.
    }}

```

Please ensure accuracy and only extract information that is clearly visible on the website pages is refereed to and extracted.
Output should strictly contain only these 14 keys mentioned "legal_name","business_email","support_email","contact_form","business_contact_numbers","business_location","phone_email_address_missing","accepts_international_orders","international_orders_evidence","shipping_countries","shipping_policy_details","has_jurisdiction_law","jurisdiction_place" and "jurisdiction_details".
If you are NOT able to find out something, just assign that specific keys as empty list or empty string, which ever is applicable. DO NOT justify. 
In case the output is not according to the instruction, please regenerate the outout in the required format.
"""