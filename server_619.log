nohup: ignoring input
INFO:     Started server process [121827]
INFO:     Waiting for application startup.
2025-08-03 20:33:07,221 - app.main - INFO - Initializing Entity Extraction API
[API_LOGGER] Log directory created/verified: /home/<USER>/Desktop/WebReview_DS_API_24Jun/api_logs
2025-08-03 20:33:07,706 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 20:33:07,706 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:33:07,707 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:07,707 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:07,825 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 20:33:07,825 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:33:07,825 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:07,825 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:07,879 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 20:33:07,879 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:33:07,879 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:07,879 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,025 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:08,025 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:08,026 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 20:33:08,026 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 20:33:08,026 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,026 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,106 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 20:33:08,106 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 20:33:08,106 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,106 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,163 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 20:33:08,163 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 20:33:08,164 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,164 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,230 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 20:33:08,230 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 20:33:08,231 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,231 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,279 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 20:33:08,279 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 20:33:08,280 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,280 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,347 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 20:33:08,347 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 20:33:08,347 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,347 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,429 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 20:33:08,429 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 20:33:08,430 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,430 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,506 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:08,506 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:33:08,636 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 20:33:08,705 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:08,705 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:08,705 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 20:33:08,705 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 20:33:08,705 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,705 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,759 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 20:33:08,759 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 20:33:08,760 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,760 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,825 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 20:33:08,825 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 20:33:08,825 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,825 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,879 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 20:33:08,879 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 20:33:08,879 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,879 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:08,957 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 20:33:08,957 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 20:33:08,958 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:08,958 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:09,043 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 20:33:09,043 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 20:33:09,043 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:09,043 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:09,116 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 20:33:09,116 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 20:33:09,116 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 20:33:09,116 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:33:09,192 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:09,192 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:33:09,305 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
INFO:     127.0.0.1:55158 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
[2025-08-03 20:33:19][EntityExtractor][background_processor][background] INFO: Java server client initialized with base URL: http://localhost:8002
2025-08-03 20:33:19,991 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:19,991 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:19,992 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:19,992 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:19,992 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:19.928505', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8002", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:19.928497", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:19,992 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:19.928505', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Java server client initialized with base URL: http://localhost:8002", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:19.928497", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:20,066 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:20,066 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:20][EntityExtractor][background_processor][background] INFO: Starting background processing for https://www.shell.in
2025-08-03 20:33:20,277 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:20,277 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:20,278 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:20,278 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:20,278 INFO sqlalchemy.engine.Engine [cached since 0.2859s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:20.192083', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:20.192073", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:20,278 - sqlalchemy.engine.Engine - INFO - [cached since 0.2859s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:20.192083', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting background processing for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:20.192073", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:20,346 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:20,346 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:20][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83",
  "website_url": "https://www.shell.in",
  "org_id": 2
}
2025-08-03 20:33:20,517 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:20,517 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:20,518 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:20,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:20,518 INFO sqlalchemy.engine.Engine [cached since 0.5257s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:20.468019', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83",  ... (20 characters truncated) ... 8-03T20:33:20.468012", "data": {"scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:20,518 - sqlalchemy.engine.Engine - INFO - [cached since 0.5257s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:20.468019', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83",  ... (20 characters truncated) ... 8-03T20:33:20.468012", "data": {"scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "website_url": "https://www.shell.in", "org_id": 2}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:20,584 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:20,584 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:33:20,765 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:20,765 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:20,769 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 20:33:20,769 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 20:33:20,769 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}
2025-08-03 20:33:20,769 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}
2025-08-03 20:33:20,846 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 20:33:20,846 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 20:33:21][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] ERROR: Error checking for existing analysis
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'entity_extraction_analysis.contact_form' in 'field list'\")\n[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id \nFROM entity_extraction_analysis \nWHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]\n[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'entity_extraction_analysis.contact_form' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/entity_extractor_orchestrator.py\", line 869, in _get_existing_analysis\n    result = session.exec(query).first()\n             ~~~~~~~~~~~~^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'entity_extraction_analysis.contact_form' in 'field list'\")\n[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id \nFROM entity_extraction_analysis \nWHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]\n[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'entity_extraction_analysis.contact_form' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/entity_extractor_orchestrator.py", line 869, in _get_existing_analysis
    result = session.exec(query).first()
             ~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'entity_extraction_analysis.contact_form' in 'field list'")
[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]
[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83:
2025-08-03 20:33:21,080 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:21,080 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:21,080 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:21,080 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:21,080 INFO sqlalchemy.engine.Engine [cached since 1.088s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:21.013833', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error checking for existing analysis", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": " ... (12264 characters truncated) ... _request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:21,080 - sqlalchemy.engine.Engine - INFO - [cached since 1.088s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:21.013833', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error checking for existing analysis", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": " ... (12264 characters truncated) ... _request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:21,204 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:21,204 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:33:21,398 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:21,398 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:21,399 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 20:33:21,399 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 20:33:21,399 INFO sqlalchemy.engine.Engine [cached since 0.63s ago] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}
2025-08-03 20:33:21,399 - sqlalchemy.engine.Engine - INFO - [cached since 0.63s ago] {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}
2025-08-03 20:33:21,482 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 20:33:21,482 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 20:33:21][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] ERROR: Error creating analysis record
{
  "error": "(pymysql.err.OperationalError) (1054, \"Unknown column 'entity_extraction_analysis.contact_form' in 'field list'\")\n[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id \nFROM entity_extraction_analysis \nWHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]\n[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)",
  "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, \"Unknown column 'entity_extraction_analysis.contact_form' in 'field list'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/entity_extractor_orchestrator.py\", line 890, in _create_analysis_record\n    existing = session.exec(existing_check).first()\n               ~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py\", line 66, in exec\n    results = super().execute(\n        statement,\n    ...<4 lines>...\n        _add_event=_add_event,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2365, in execute\n    return self._execute_internal(\n           ~~~~~~~~~~~~~~~~~~~~~~^\n        statement,\n        ^^^^^^^^^^\n    ...<4 lines>...\n        _add_event=_add_event,\n        ^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py\", line 2251, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self,\n        ^^^^^\n    ...<4 lines>...\n        conn,\n        ^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py\", line 305, in orm_execute_statement\n    result = conn.execute(\n        statement, params or {}, execution_options=execution_options\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1416, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py\", line 515, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1638, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1843, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1983, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 2352, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py\", line 1964, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py\", line 942, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n    ~~~~~~~~~~^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n    ~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, \"Unknown column 'entity_extraction_analysis.contact_form' in 'field list'\")\n[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id \nFROM entity_extraction_analysis \nWHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]\n[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"
}Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'entity_extraction_analysis.contact_form' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/entity_extractor_orchestrator.py", line 890, in _create_analysis_record
    existing = session.exec(existing_check).first()
               ~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlmodel/orm/session.py", line 66, in exec
    results = super().execute(
        statement,
    ...<4 lines>...
        _add_event=_add_event,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
    ~~~~~~~~~~^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'entity_extraction_analysis.contact_form' in 'field list'")
[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]
[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

Traceback for analysis orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83:
2025-08-03 20:33:21,876 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:21,876 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:21,876 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:21,876 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:21,876 INFO sqlalchemy.engine.Engine [cached since 1.884s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:21.716139', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error creating analysis record", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-0 ... (12281 characters truncated) ... _request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:21,876 - sqlalchemy.engine.Engine - INFO - [cached since 1.884s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:21.716139', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error creating analysis record", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-0 ... (12281 characters truncated) ... _request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\\n"}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:22,011 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:22,011 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:22][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] ERROR: Entity extraction failed: (pymysql.err.OperationalError) (1054, "Unknown column 'entity_extraction_analysis.contact_form' in 'field list'")
[SQL: SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.contact_form, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.phone_email_address_missing, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.international_orders_evidence, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC]
[parameters: {'scrape_request_ref_id_1': '687971e6-3639-4f09-a702-e438f3f4ee83', 'org_id_1': 2}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-03 20:33:22,201 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:22,201 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:22,202 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:22,202 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:22,202 INFO sqlalchemy.engine.Engine [cached since 2.21s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:22.146038', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Entity extraction failed: (pymysql.err.OperationalError) (1054, \\"Unknown column \'entity_extraction_analysis.contact ... (1929 characters truncated) ... ps://sqlalche.me/e/20/e3q8)", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T20:33:22.146021", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:22,202 - sqlalchemy.engine.Engine - INFO - [cached since 2.21s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:22.146038', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Entity extraction failed: (pymysql.err.OperationalError) (1054, \\"Unknown column \'entity_extraction_analysis.contact ... (1929 characters truncated) ... ps://sqlalche.me/e/20/e3q8)", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T20:33:22.146021", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:22,277 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:22,277 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:33:22,464 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:22,464 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:22,465 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, contact_form, business_contact_numbers, business_location, phone_email_address_missing, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, international_orders_evidence, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(contact_form)s, %(business_contact_numbers)s, %(business_location)s, %(phone_email_address_missing)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(international_orders_evidence)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 20:33:22,465 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, contact_form, business_contact_numbers, business_location, phone_email_address_missing, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, international_orders_evidence, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(contact_form)s, %(business_contact_numbers)s, %(business_location)s, %(phone_email_address_missing)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(international_orders_evidence)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 20:33:22,465 INFO sqlalchemy.engine.Engine [generated in 0.00021s] {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'contact_form': None, 'business_contact_numbers': None, 'business_location': None, 'phone_email_address_missing': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'international_orders_evidence': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T20:33:22.398136', 'started_at': '2025-08-03T20:33:22.398136', 'completed_at': None, 'error_message': '(pymysql.err.OperationalError) (1054, "Unknown column \'entity_extraction_analysis.contact_form\' in \'field list\'")\n[SQL: SELECT entity_extraction ... (1741 characters truncated) ... 'scrape_request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'org_id': 2}
2025-08-03 20:33:22,465 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'contact_form': None, 'business_contact_numbers': None, 'business_location': None, 'phone_email_address_missing': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'international_orders_evidence': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T20:33:22.398136', 'started_at': '2025-08-03T20:33:22.398136', 'completed_at': None, 'error_message': '(pymysql.err.OperationalError) (1054, "Unknown column \'entity_extraction_analysis.contact_form\' in \'field list\'")\n[SQL: SELECT entity_extraction ... (1741 characters truncated) ... 'scrape_request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'org_id': 2}
2025-08-03 20:33:22,545 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 20:33:22,545 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 20:33:22][EntityExtractor][orchestrator_687971e6-3639-4f09-a702-e438f3f4ee83][687971e6-3639-4f09-a702-e438f3f4ee83] ERROR: Error storing failed analysis: (pymysql.err.OperationalError) (1054, "Unknown column 'contact_form' in 'field list'")
[SQL: INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, contact_form, business_contact_numbers, business_location, phone_email_address_missing, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, international_orders_evidence, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(contact_form)s, %(business_contact_numbers)s, %(business_location)s, %(phone_email_address_missing)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(international_orders_evidence)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)]
[parameters: {'scrape_request_ref_id': '687971e6-3639-4f09-a702-e438f3f4ee83', 'website_url': 'https://www.shell.in', 'processing_status': 'FAILED', 'legal_name': None, 'business_email': None, 'support_email': None, 'contact_form': None, 'business_contact_numbers': None, 'business_location': None, 'phone_email_address_missing': None, 'has_jurisdiction_law': None, 'jurisdiction_details': None, 'accepts_international_orders': None, 'international_orders_evidence': None, 'shipping_policy_details': None, 'jurisdiction_place': None, 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': None, 'urls_not_reachable_by_gemini': None, 'extraction_method': None, 'total_urls_processed': None, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T20:33:22.398136', 'started_at': '2025-08-03T20:33:22.398136', 'completed_at': None, 'error_message': '(pymysql.err.OperationalError) (1054, "Unknown column \'entity_extraction_analysis.contact_form\' in \'field list\'")\n[SQL: SELECT entity_extraction ... (1741 characters truncated) ... 'scrape_request_ref_id_1\': \'687971e6-3639-4f09-a702-e438f3f4ee83\', \'org_id_1\': 2}]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'org_id': 2}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-03 20:33:22,704 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:22,704 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:22,705 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:22,705 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:22,705 INFO sqlalchemy.engine.Engine [cached since 2.713s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:22.637891', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error storing failed analysis: (pymysql.err.OperationalError) (1054, \\"Unknown column \'contact_form\' in \'field lis ... (2945 characters truncated) ... ps://sqlalche.me/e/20/e3q8)", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T20:33:22.637883", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:22,705 - sqlalchemy.engine.Engine - INFO - [cached since 2.713s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:22.637891', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Error storing failed analysis: (pymysql.err.OperationalError) (1054, \\"Unknown column \'contact_form\' in \'field lis ... (2945 characters truncated) ... ps://sqlalche.me/e/20/e3q8)", "scrape_request_ref_id": "687971e6-3639-4f09-a702-e438f3f4ee83", "timestamp": "2025-08-03T20:33:22.637883", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:22,785 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:22,785 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:22][EntityExtractor][background_processor][background] INFO: Entity extraction completed for https://www.shell.in
2025-08-03 20:33:22,944 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:22,944 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:22,945 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:22,945 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:22,945 INFO sqlalchemy.engine.Engine [cached since 2.953s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:22.877998', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:22.877993", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:22,945 - sqlalchemy.engine.Engine - INFO - [cached since 2.953s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:22.877998', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Entity extraction completed for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:22.877993", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,024 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:23,024 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:23][EntityExtractor][background_processor][background] INFO: Sending extraction results to Java server (sync) for https://www.shell.in
2025-08-03 20:33:23,184 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:23,184 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:23,185 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,185 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,185 INFO sqlalchemy.engine.Engine [cached since 3.193s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.118054', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.118047", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,185 - sqlalchemy.engine.Engine - INFO - [cached since 3.193s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.118054', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending extraction results to Java server (sync) for https://www.shell.in", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.118047", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,265 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:23,265 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:23][EntityExtractor][background_processor][background] INFO: Sending PATCH request to: http://localhost:8002/api/entity-extraction/results
2025-08-03 20:33:23,425 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:23,425 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:23,425 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,425 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,426 INFO sqlalchemy.engine.Engine [cached since 3.434s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.358623', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8002/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.358618", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,426 - sqlalchemy.engine.Engine - INFO - [cached since 3.434s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.358623', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Sending PATCH request to: http://localhost:8002/api/entity-extraction/results", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.358618", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,505 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:23,505 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:23][EntityExtractor][background_processor][background] DEBUG: Payload org_id type: <class 'int'>, value: 2
2025-08-03 20:33:23,665 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:23,665 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:23,665 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,665 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,666 INFO sqlalchemy.engine.Engine [cached since 3.674s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.597405', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.597397", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,666 - sqlalchemy.engine.Engine - INFO - [cached since 3.674s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.597405', 'type': 'entity_extractor', 'messages': '{"level": "DEBUG", "message": "Payload org_id type: <class \'int\'>, value: 2", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.597397", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,745 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:23,745 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:23][EntityExtractor][background_processor][background] ERROR: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 20:33:23,907 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:23,907 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:23,907 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,907 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:23,907 INFO sqlalchemy.engine.Engine [cached since 3.915s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.865579', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.865573", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,907 - sqlalchemy.engine.Engine - INFO - [cached since 3.915s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:23.865579', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:23.865573", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:23,985 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:23,985 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 20:33:24][EntityExtractor][background_processor][background] ERROR: Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused
2025-08-03 20:33:24,157 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 20:33:24,157 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:33:24,158 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:24,158 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 20:33:24,158 INFO sqlalchemy.engine.Engine [cached since 4.166s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:24.108659', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:24.108647", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:24,158 - sqlalchemy.engine.Engine - INFO - [cached since 4.166s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T20:33:24.108659', 'type': 'entity_extractor', 'messages': '{"level": "ERROR", "message": "Failed to send results to Java server: Request error sending results to Java server: [Errno 111] Connection refused", "scrape_request_ref_id": "background", "timestamp": "2025-08-03T20:33:24.108647", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 20:33:24,286 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 20:33:24,286 - sqlalchemy.engine.Engine - INFO - COMMIT
