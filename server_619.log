nohup: ignoring input
Traceback (most recent call last):
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/bin/uvicorn", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/main.py", line 412, in main
    run(
    ~~~^
        app,
        ^^^^
    ...<45 lines>...
        h11_max_incomplete_event_size=h11_max_incomplete_event_size,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/main.py", line 579, in run
    server.run()
    ~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/main.py", line 5, in <module>
    from app.Extractor.routers.entity_extraction_router import router as entity_router
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/routers/entity_extraction_router.py", line 21, in <module>
    from app.Extractor.services.entity_extractor_orchestrator import EntityExtractorOrchestrator
  File "/home/<USER>/Desktop/WebReview_DS_API_24Jun/app/Extractor/services/entity_extractor_orchestrator.py", line 591
    accepts_international_orders=merged_result.get("accepts_international_orders"),  # Keep as string: "yes"/"no"/"unclear"
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
SyntaxError: keyword argument repeated: accepts_international_orders
