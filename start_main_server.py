#!/usr/bin/env python3
"""
Start Main Server on Port 8081

This script starts the main entity extraction API server on port 8081
for testing with the dummy Java server on port 8082.
"""

import uvicorn
import os
import sys
from pathlib import Path

def main():
    print("=" * 60)
    print("MAIN ENTITY EXTRACTION SERVER")
    print("=" * 60)
    print("Starting the main entity extraction API server on port 8081")
    print("")
    print("Server will run on: http://localhost:8081")
    print("API docs: http://localhost:8081/docs")
    print("Health check: http://localhost:8081/health")
    print("")
    print("Make sure the dummy Java server is running on port 8082")
    print("for PATCH call testing.")
    print("=" * 60)
    
    # Set environment variable for Java server URL if not already set
    if not os.getenv("JAVA_SERVER_BASE_URL"):
        os.environ["JAVA_SERVER_BASE_URL"] = "http://localhost:8082"
        print(f"Set JAVA_SERVER_BASE_URL to: {os.environ['JAVA_SERVER_BASE_URL']}")
    
    # Start the server
    try:
        uvicorn.run(
            "app.main:app",  # Assuming the FastAPI app is in app.main
            host="0.0.0.0",
            port=8081,
            reload=True,  # Enable auto-reload for development
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
